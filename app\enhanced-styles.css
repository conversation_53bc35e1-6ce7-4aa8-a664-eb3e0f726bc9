/* Additional Enhanced Styles - Part 2 */

.nav-links a::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 25px;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.nav-links a:hover::before {
    opacity: 1;
}

.nav-links a:hover {
    color: #fff;
    transform: translateY(-2px);
}

/* Hero Section */
.hero {
    min-height: 100vh;
    display: flex;
    align-items: center;
    justify-content: center;
    text-align: center;
    padding: 8rem 2rem 4rem;
    position: relative;
    background: radial-gradient(circle at 30% 70%, rgba(139, 92, 246, 0.15) 0%, transparent 50%),
                radial-gradient(circle at 70% 30%, rgba(6, 182, 212, 0.15) 0%, transparent 50%);
}

.hero::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background:
        radial-gradient(circle at 20% 80%, rgba(139, 92, 246, 0.1) 0%, transparent 50%),
        radial-gradient(circle at 80% 20%, rgba(6, 182, 212, 0.1) 0%, transparent 50%),
        radial-gradient(circle at 40% 40%, rgba(236, 72, 153, 0.08) 0%, transparent 50%);
    animation: pulse 8s ease-in-out infinite;
    pointer-events: none;
}

@keyframes pulse {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.7; }
}

/* Palette-specific hero animations */
body.palette-C .hero::before {
    background:
        radial-gradient(circle at 20% 80%, rgba(14, 165, 233, 0.15) 0%, transparent 50%),
        radial-gradient(circle at 80% 20%, rgba(20, 184, 166, 0.15) 0%, transparent 50%),
        radial-gradient(circle at 40% 40%, rgba(2, 132, 199, 0.1) 0%, transparent 50%);
}

body.palette-B .hero::before {
    background:
        radial-gradient(circle at 20% 80%, rgba(212, 175, 55, 0.15) 0%, transparent 50%),
        radial-gradient(circle at 80% 20%, rgba(184, 148, 31, 0.15) 0%, transparent 50%),
        radial-gradient(circle at 40% 40%, rgba(139, 139, 139, 0.1) 0%, transparent 50%);
}

.hero-content {
    max-width: 800px;
    animation: hero-fade-in 1.5s ease-out;
    position: relative;
    z-index: 1;
}

.hero h1 {
    font-size: clamp(2.5rem, 8vw, 5rem);
    font-weight: 900;
    margin-bottom: 1.5rem;
    color: #fff;
    text-shadow: 0 0 40px rgba(255, 255, 255, 0.3);
    line-height: 1.1;
}

.hero .highlight {
    background: linear-gradient(135deg, #8b5cf6, #06b6d4);
    background-clip: text;
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    color: #8b5cf6; /* Fallback for unsupported browsers */
}

.hero p {
    font-size: 1.3rem;
    margin-bottom: 3rem;
    color: rgba(255, 255, 255, 0.9);
    line-height: 1.7;
}

/* Morphing CTA Button */
.cta-button {
    display: inline-block;
    padding: 1.2rem 3rem;
    background: linear-gradient(135deg, #8b5cf6, #06b6d4, #ec4899);
    background-size: 200% 200%;
    color: white;
    text-decoration: none;
    border-radius: 50px;
    font-weight: 700;
    font-size: 1.1rem;
    transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
    box-shadow:
        0 15px 35px rgba(139, 92, 246, 0.4),
        0 5px 15px rgba(6, 182, 212, 0.3);
    position: relative;
    overflow: hidden;
    transform-style: preserve-3d;
    animation: gradient-shift 3s ease infinite;
}

@keyframes gradient-shift {
    0% { background-position: 0% 50%; }
    50% { background-position: 100% 50%; }
    100% { background-position: 0% 50%; }
}

.cta-button::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
    transition: left 0.6s ease;
}

.cta-button:hover::before {
    left: 100%;
}

.cta-button:hover {
    transform: translateY(-8px) scale(1.05);
    box-shadow:
        0 25px 50px rgba(139, 92, 246, 0.6),
        0 10px 30px rgba(6, 182, 212, 0.4),
        0 0 50px rgba(236, 72, 153, 0.3);
}

/* Content sections */
.section {
    padding: 6rem 2rem;
    position: relative;
}

.section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 50%;
    transform: translateX(-50%);
    width: 90%;
    height: 1px;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);
}

.section-container {
    max-width: 1200px;
    margin: 0 auto;
}

.section h2 {
    font-size: 2.5rem;
    margin-bottom: 3rem;
    text-align: center;
    color: #fff;
    text-shadow: 0 0 20px rgba(255, 255, 255, 0.3);
}

.about-content {
    max-width: 800px;
    margin: 0 auto;
    text-align: left;
}

.about-content p {
    font-size: 1.1rem;
    line-height: 1.8;
    margin-bottom: 2rem;
    color: rgba(255, 255, 255, 0.9);
}

.about-content strong {
    color: #8b5cf6;
}

/* Services Grid - 2x2 Layout */
.services-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 2rem;
    margin-top: 3rem;
    max-width: 800px;
    margin-left: auto;
    margin-right: auto;
}

/* 3D Floating Cards Effect */
.service-card {
    background: rgba(255, 255, 255, 0.08);
    padding: 2.5rem;
    border-radius: 20px;
    border: 1px solid rgba(255, 255, 255, 0.15);
    backdrop-filter: blur(15px);
    -webkit-backdrop-filter: blur(15px);
    transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
    position: relative;
    overflow: hidden;
    transform-style: preserve-3d;
    perspective: 1000px;
}

.service-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg,
        rgba(139, 92, 246, 0.2) 0%,
        rgba(6, 182, 212, 0.2) 50%,
        rgba(236, 72, 153, 0.2) 100%);
    opacity: 0;
    transition: opacity 0.4s ease;
    border-radius: 20px;
}

.service-card::after {
    content: '';
    position: absolute;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    background: conic-gradient(
        from 0deg,
        transparent 0deg,
        rgba(139, 92, 246, 0.3) 90deg,
        transparent 180deg,
        rgba(6, 182, 212, 0.3) 270deg,
        transparent 360deg
    );
    animation: rotate 4s linear infinite;
    opacity: 0;
    transition: opacity 0.4s ease;
}

@keyframes rotate {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.service-card:hover::before {
    opacity: 1;
}

.service-card:hover::after {
    opacity: 0.7;
}

.service-card:hover {
    transform: translateY(-20px) rotateX(5deg) rotateY(5deg);
    box-shadow:
        0 30px 60px rgba(139, 92, 246, 0.3),
        0 0 50px rgba(6, 182, 212, 0.2);
    border-color: rgba(139, 92, 246, 0.5);
}

/* Magnetic Hover Effect for Icons */
.service-icon {
    font-size: 3.5rem;
    margin-bottom: 1rem;
    display: block;
    transition: all 0.3s ease;
    position: relative;
    z-index: 10;
    filter: drop-shadow(0 0 10px rgba(139, 92, 246, 0.5));
}

.service-card:hover .service-icon {
    transform: scale(1.2) rotateY(15deg);
    filter: drop-shadow(0 0 20px rgba(139, 92, 246, 0.8));
}

/* Glowing Text Effect */
.service-card h3 {
    font-size: 1.4rem;
    margin-bottom: 1rem;
    color: #fff;
    position: relative;
    z-index: 10;
    text-shadow: 0 0 10px rgba(255, 255, 255, 0.3);
    transition: all 0.3s ease;
}

.service-card:hover h3 {
    text-shadow: 0 0 20px rgba(139, 92, 246, 0.8);
    transform: translateZ(20px);
}

/* Interactive Background Orbs */
.orb {
    position: absolute;
    border-radius: 50%;
    background: radial-gradient(circle at 30% 30%, rgba(139, 92, 246, 0.6), rgba(6, 182, 212, 0.4));
    filter: blur(60px);
    animation: float-orb 20s ease-in-out infinite;
    pointer-events: none;
}

.orb:nth-child(1) {
    width: 300px;
    height: 300px;
    top: 20%;
    left: 10%;
    animation-delay: 0s;
}

.orb:nth-child(2) {
    width: 200px;
    height: 200px;
    top: 60%;
    right: 10%;
    animation-delay: 5s;
}

.orb:nth-child(3) {
    width: 150px;
    height: 150px;
    bottom: 20%;
    left: 60%;
    animation-delay: 10s;
}

@keyframes float-orb {
    0%, 100% {
        transform: translateY(0px) translateX(0px) scale(1);
        opacity: 0.6;
    }
    33% {
        transform: translateY(-30px) translateX(20px) scale(1.1);
        opacity: 0.8;
    }
    66% {
        transform: translateY(20px) translateX(-15px) scale(0.9);
        opacity: 0.7;
    }
}

/* Contact Section - Keeping current structure */
.cta-bottom {
    text-align: center;
    padding: 6rem 2rem;
    background: rgba(19, 37, 49, 0.8);
    border-top: 1px solid rgba(255, 255, 255, 0.1);
    position: relative;
}

.cta-bottom::before {
    content: '';
    position: absolute;
    top: 0;
    left: 50%;
    transform: translateX(-50%);
    width: 90%;
    height: 1px;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);
}

.cta-bottom h2 {
    font-size: 2.5rem;
    margin-bottom: 2rem;
    color: #fff;
    text-shadow: 0 0 20px rgba(255, 255, 255, 0.3);
}

.cta-bottom p {
    font-size: 1.2rem;
    margin-bottom: 3rem;
    color: rgba(255, 255, 255, 0.9);
    max-width: 600px;
    margin-left: auto;
    margin-right: auto;
    margin-bottom: 3rem;
}

.cta-bottom button {
    background: linear-gradient(135deg, #8b5cf6, #06b6d4, #ec4899);
    background-size: 200% 200%;
    color: white;
    padding: 1.2rem 3rem;
    border: none;
    border-radius: 50px;
    font-weight: 700;
    font-size: 1.1rem;
    cursor: pointer;
    transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
    box-shadow:
        0 15px 35px rgba(139, 92, 246, 0.4),
        0 5px 15px rgba(6, 182, 212, 0.3);
    animation: gradient-shift 3s ease infinite;
}

.cta-bottom button:hover {
    transform: translateY(-8px) scale(1.05);
    box-shadow:
        0 25px 50px rgba(139, 92, 246, 0.6),
        0 10px 30px rgba(6, 182, 212, 0.4),
        0 0 50px rgba(236, 72, 153, 0.3);
}

.cta-link {
    text-decoration: none;
    display: inline-block;
}

/* Footer */
.footer {
    padding: 4rem 2rem 2rem;
    text-align: center;
    background: rgba(0, 0, 0, 0.5);
    border-top: 1px solid rgba(255, 255, 255, 0.1);
}

.footer-content {
    max-width: 1200px;
    margin: 0 auto;
}

.footer-links {
    display: flex;
    justify-content: center;
    gap: 2rem;
    margin-bottom: 2rem;
    flex-wrap: wrap;
}

.footer-links a {
    color: rgba(255, 255, 255, 0.6);
    text-decoration: none;
    transition: color 0.3s ease;
}

.footer-links a:hover {
    color: #8b5cf6;
}

.copyright {
    color: rgba(255, 255, 255, 0.4);
    font-size: 0.9rem;
}

/* Animations */
@keyframes hero-fade-in {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.fade-in {
    opacity: 0;
    transform: translateY(20px);
    transition: all 0.8s ease;
}

.fade-in.visible {
    opacity: 1;
    transform: translateY(0);
}

/* Mobile menu button */
.mobile-menu-btn {
    display: none;
    background: none;
    border: none;
    color: white;
    font-size: 1.5rem;
    cursor: pointer;
}

/* Responsive Design */
@media (max-width: 768px) {
    .nav-links {
        display: none;
    }

    .mobile-menu-btn {
        display: block;
    }

    .hero {
        padding: 6rem 1rem 2rem;
    }

    .section {
        padding: 4rem 1rem;
    }

    .services-grid {
        grid-template-columns: 1fr;
        max-width: 100%;
    }

    .service-card:hover {
        transform: translateY(-10px);
    }

    .orb {
        display: none; /* Hide orbs on mobile for performance */
    }

    .hero h1 {
        font-size: 2.5rem;
    }

    .section h2 {
        font-size: 2rem;
    }
}

/* Smooth scrolling */
html {
    scroll-behavior: smooth;
}

/* Performance optimizations */
.service-card,
.cta-button {
    will-change: transform;
}

/* Particles */
.particles {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    pointer-events: none;
    z-index: -1;
}

.particle {
    position: absolute;
    width: 2px;
    height: 2px;
    background: rgba(255, 255, 255, 0.3);
    border-radius: 50%;
    animation: float 8s linear infinite;
}

@keyframes float {
    0% {
        transform: translateY(100vh) rotate(0deg);
        opacity: 0;
    }
    10% {
        opacity: 1;
    }
    90% {
        opacity: 1;
    }
    100% {
        transform: translateY(-100vh) rotate(360deg);
        opacity: 0;
    }
}

/* Reduced motion for accessibility */
@media (prefers-reduced-motion: reduce) {
    * {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
    }

    .particle {
        animation: none;
    }
}
