:root {
  /* Default palette (A) */
  --main-background: #0b1a24;
  --cta-button: linear-gradient(135deg, #FF6B00 0%, #FF8124 100%);
  --logo-gradient: linear-gradient(135deg, #FAFAFA 0%, #8B8B8B 30%, #FF6B00 65%, #FAFAFA 100%);
  --service-cards-hover: rgba(255, 107, 0, 0.08);
  --text-highlights: linear-gradient(135deg, #FF6B00, #FF8124);
  --secondary-elements: linear-gradient(135deg, #132531 0%, #1f3544 100%);
}

body {
  margin: 0;
  font-family: 'Segoe UI', sans-serif;
  background: var(--main-background);
  color: #d8d8d8;
  transition: background-color 0.5s ease, color 0.5s ease;
}

/* Palette-specific overrides */
body.palette-B {
  background: linear-gradient(135deg, #0B0B0B 0%, #1A1A1A 25%, #2D2D2D 50%, #4A4A4A 75%, #D4AF37 100%);
  color: #FAFAFA;
}

/* Palette C - Deep Space Universe */
body.palette-C {
  background: linear-gradient(135deg, #0A0A0A 0%, #1E293B 25%, #334155 50%, #475569 75%, #0EA5E9 100%);
  color: #F8FAFC;
}
header {
  text-align: center;
  padding: 2rem 1rem 1rem;
  display: flex;
  justify-content: center;
}

.logo-container {
  width: 100%;
  max-width: 500px;
  display: flex;
  justify-content: center;
  margin: 0 auto 1.5rem;
}

.logo {
  max-width: 100%;
  height: auto;
  filter: drop-shadow(0px 5px 8px rgba(0, 0, 0, 0.7)) drop-shadow(0px 10px 15px rgba(0, 0, 0, 0.4));
  transition: transform 0.3s ease, filter 0.3s ease;
}

.logo:hover {
  transform: scale(1.03);
  filter: drop-shadow(0px 8px 12px rgba(0, 0, 0, 0.8)) drop-shadow(0px 15px 25px rgba(0, 0, 0, 0.5));
}
nav {
  position: fixed;
  top: 15%;
  left: 0;
  transform: translateY(-15%);
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
  padding: 1.2rem;
}
nav a {
  color: #ffffff;
  text-decoration: none;
  font-weight: bold;
  transition: color 0.3s ease, transform 0.3s ease;
  padding: 0.5rem 1rem;
  border-radius: 5px;
  text-shadow: 0 0 5px rgba(0, 0, 0, 0.5);
}

nav a:hover {
  color: #1e90ff;
  transform: translateX(5px);
}
.hero {
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  text-align: center;
  padding: 8rem 2rem 4rem;
  position: relative;
}
.hero h1 {
  font-size: clamp(2.5rem, 8vw, 5rem);
  font-weight: 900;
  margin-bottom: 1.5rem;
  color: #fff;
  text-shadow: 0 0 40px rgba(255, 255, 255, 0.3);
  line-height: 1.1;
}
.hero p {
  font-size: 1.3rem;
  max-width: 600px;
  margin: 1rem auto 3rem;
  color: rgba(255, 255, 255, 0.9);
  line-height: 1.7;
}
.hero .cta-button {
  margin-top: 2rem;
}
.hero .cta-button button {
  background: var(--cta-button);
  color: white;
  padding: 1rem 2rem;
  border: none;
  border-radius: 8px;
  font-weight: bold;
  cursor: pointer;
  font-size: 1.1rem;
  transition: all 0.3s ease;
  text-shadow: 0 0 5px rgba(0, 0, 0, 0.5);
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.3);
}

.hero .cta-button button:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(0, 0, 0, 0.4);
  filter: brightness(1.1);
}

.cta-link {
  text-decoration: none;
  display: inline-block;
}
section {
  padding: 3rem 1.5rem;
  max-width: 900px;
  margin: auto;
}
h2 {
  color: #d8d8d8;
  font-size: 1.8rem;
  text-transform: uppercase;
}
.services {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 2rem;
  margin-top: 2rem;
}
.service {
  background: var(--secondary-elements);
  padding: 1.5rem;
  border-radius: 10px;
  border: 1px solid rgba(255, 255, 255, 0.1);
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
  transition: all 0.3s ease;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
}

.service:hover {
  background: var(--service-cards-hover);
  transform: translateY(-5px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3);
  border-color: rgba(255, 255, 255, 0.2);
}

.service-icon {
  font-size: 2.5rem;
  margin-bottom: 1rem;
  background: var(--text-highlights);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  transition: all 0.3s ease;
}

.service:hover .service-icon {
  transform: scale(1.1);
  filter: drop-shadow(0 0 10px rgba(255, 255, 255, 0.3));
}
.service h3 {
  margin-top: 0;
}
.cta-bottom {
  text-align: center;
  padding: 3rem 1.5rem;
  background: var(--secondary-elements);
  border-radius: 12px;
  margin: 2rem 1rem;
}
.cta-bottom button {
  background: var(--cta-button);
  color: white;
  padding: 1rem 2rem;
  border: none;
  border-radius: 8px;
  font-weight: bold;
  cursor: pointer;
  font-size: 1.1rem;
  transition: all 0.3s ease;
  text-shadow: 0 0 5px rgba(0, 0, 0, 0.5);
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.3);
}

.cta-bottom button:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(0, 0, 0, 0.4);
  filter: brightness(1.1);
}
footer {
  text-align: center;
  padding: 2rem 1rem;
  font-size: 0.9rem;
  color: #999;
}
/* Palette-specific enhancements */
body.palette-B .service {
  background: linear-gradient(135deg, #1A1A1A 0%, #2D2D2D 50%, #4A4A4A 100%);
  border: 1px solid rgba(212, 175, 55, 0.2);
}

body.palette-B .service:hover {
  background: rgba(212, 175, 55, 0.08);
  border-color: rgba(212, 175, 55, 0.4);
  box-shadow: 0 8px 25px rgba(212, 175, 55, 0.2);
}

body.palette-B .cta-bottom {
  background: linear-gradient(135deg, #1A1A1A 0%, #2D2D2D 50%, #4A4A4A 100%);
  border: 1px solid rgba(212, 175, 55, 0.2);
}

body.palette-B h1,
body.palette-B h2,
body.palette-B h3 {
  background: linear-gradient(135deg, #D4AF37, #B8941F, #8B8B8B);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

body.palette-B .logo {
  filter: drop-shadow(0px 5px 8px rgba(212, 175, 55, 0.3))
          drop-shadow(0px 10px 15px rgba(0, 0, 0, 0.4));
}

body.palette-B .logo:hover {
  filter: drop-shadow(0px 8px 12px rgba(212, 175, 55, 0.5))
          drop-shadow(0px 15px 25px rgba(0, 0, 0, 0.6));
}

body.palette-C .service {
  background: linear-gradient(135deg, #0A0A0A 0%, #0F172A 25%, #1E293B 50%, #334155 75%, #14B8A6 100%);
  border: 1px solid rgba(14, 165, 233, 0.2);
}

body.palette-C .service:hover {
  background: rgba(14, 165, 233, 0.08);
  border-color: rgba(14, 165, 233, 0.4);
  box-shadow: 0 8px 25px rgba(14, 165, 233, 0.2);
}

body.palette-C .cta-bottom {
  background: linear-gradient(135deg, #0A0A0A 0%, #0F172A 25%, #1E293B 50%, #334155 75%, #14B8A6 100%);
  border: 1px solid rgba(14, 165, 233, 0.2);
}

body.palette-C h1,
body.palette-C h2,
body.palette-C h3 {
  background: linear-gradient(135deg, #0EA5E9, #14B8A6, #0284C7);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

body.palette-C .logo {
  filter: drop-shadow(0px 5px 8px rgba(14, 165, 233, 0.3))
          drop-shadow(0px 10px 15px rgba(0, 0, 0, 0.4));
}

body.palette-C .logo:hover {
  filter: drop-shadow(0px 8px 12px rgba(14, 165, 233, 0.5))
          drop-shadow(0px 15px 25px rgba(0, 0, 0, 0.6));
}

@media(max-width: 768px) {
  .services {
    grid-template-columns: 1fr;
  }
  nav {
    display: none;
  }
}
